// ==UserScript==
// @name         Augment Code 社区版切换工具
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  在 app.augmentcode.com 上添加一个美观的浮动窗口，用于切换到社区版计划
// <AUTHOR>
// @match        https://app.augmentcode.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @run-at       document-end
// ==/UserScript==

(function () {
    'use strict';

    // 添加CSS样式
    GM_addStyle(`
        #augment-float-window {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        #augment-float-window:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .augment-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .augment-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            text-align: center;
        }

        .augment-content {
            padding: 20px;
        }

        .augment-button {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 16px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .augment-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .augment-button:active {
            transform: translateY(0);
        }

        .augment-button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .augment-status {
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-idle {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-processing {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            border: 1px solid rgba(52, 152, 219, 0.3);
            animation: pulse 2s infinite;
        }

        .status-success {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .status-error {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .augment-close {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .augment-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .augment-footer {
            padding: 12px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
            border-radius: 0 0 12px 12px;
        }

        .augment-promotion {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px 8px;
            border-radius: 6px;
            user-select: none;
        }

        .augment-promotion:hover {
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
        }

        .augment-promotion:active {
            transform: translateY(0);
        }

        .wechat-icon {
            font-size: 14px;
            color: #07c160;
        }

        .copy-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            margin-bottom: 4px;
        }

        .copy-tooltip.show {
            opacity: 1;
        }

        .copy-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 4px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
        }
    `);

    // 创建浮动窗口
    function createFloatingWindow() {
        const floatWindow = document.createElement('div');
        floatWindow.id = 'augment-float-window';
        floatWindow.innerHTML = `
            <button class="augment-close" onclick="this.parentElement.style.display='none'">×</button>
            <div class="augment-header">
                <h3 class="augment-title">🚀 社区版切换工具</h3>
            </div>
            <div class="augment-content">
                <button id="augment-action-btn" class="augment-button">
                    切换到社区版计划
                </button>
                <div id="augment-status" class="augment-status status-idle">
                    待操作 - 点击按钮开始切换
                </div>
            </div>
            <div class="augment-footer">
                <div id="augment-promotion" class="augment-promotion" title="点击复制公众号名称">
                    <span class="wechat-icon">📱</span>
                    <span>关注公众号：@煎饼果子卷AI</span>
                    <div class="copy-tooltip">已复制到剪贴板！</div>
                </div>
            </div>
        `;

        document.body.appendChild(floatWindow);
        return floatWindow;
    }

    // 更新状态显示
    function updateStatus(message, type) {
        const statusElement = document.getElementById('augment-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `augment-status status-${type}`;
        }
    }

    // 复制公众号名称到剪贴板
    function copyWechatName() {
        const wechatName = '@煎饼果子卷AI';

        // 尝试使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(wechatName).then(() => {
                showCopyTooltip();
            }).catch(() => {
                // 如果失败，使用传统方法
                fallbackCopyTextToClipboard(wechatName);
            });
        } else {
            // 使用传统的复制方法
            fallbackCopyTextToClipboard(wechatName);
        }
    }

    // 传统的复制方法（兼容性更好）
    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showCopyTooltip();
        } catch (err) {
            console.error('复制失败:', err);
        }

        document.body.removeChild(textArea);
    }

    // 显示复制成功提示
    function showCopyTooltip() {
        const tooltip = document.querySelector('.copy-tooltip');
        if (tooltip) {
            tooltip.classList.add('show');
            setTimeout(() => {
                tooltip.classList.remove('show');
            }, 2000);
        }
    }

    // 发送API请求
    function switchToCommunityPlan() {
        const button = document.getElementById('augment-action-btn');

        // 禁用按钮并更新状态
        button.disabled = true;
        button.textContent = '处理中...';
        updateStatus('正在发送请求...', 'processing');

        // 发送POST请求
        GM_xmlhttpRequest({
            method: 'POST',
            url: 'https://app.augmentcode.com/api/put-user-on-plan',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            data: JSON.stringify({
                planId: 'orb_community_plan'
            }),
            onload: function (response) {
                try {
                    const result = JSON.parse(response.responseText);

                    if (result.success) {
                        updateStatus(`✅ 成功！${result.message}`, 'success');
                        button.textContent = '切换成功';

                        // 3秒后恢复按钮状态
                        setTimeout(() => {
                            button.disabled = false;
                            button.textContent = '切换到社区版计划';
                            updateStatus('待操作 - 点击按钮开始切换', 'idle');
                        }, 3000);
                    } else {
                        updateStatus(`❌ 操作失败：${result.message || '未知错误'}`, 'error');
                        button.disabled = false;
                        button.textContent = '重试切换';
                    }
                } catch (e) {
                    updateStatus('❌ 响应解析失败', 'error');
                    button.disabled = false;
                    button.textContent = '重试切换';
                }
            },
            onerror: function (error) {
                updateStatus('❌ 网络请求失败', 'error');
                button.disabled = false;
                button.textContent = '重试切换';
                console.error('API请求失败:', error);
            },
            ontimeout: function () {
                updateStatus('❌ 请求超时', 'error');
                button.disabled = false;
                button.textContent = '重试切换';
            },
            timeout: 10000 // 10秒超时
        });
    }

    // 初始化脚本
    function init() {
        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 创建浮动窗口
        const floatWindow = createFloatingWindow();

        // 绑定按钮点击事件
        const actionButton = document.getElementById('augment-action-btn');
        if (actionButton) {
            actionButton.addEventListener('click', switchToCommunityPlan);
        }

        // 绑定公众号推广区域点击事件
        const promotionElement = document.getElementById('augment-promotion');
        if (promotionElement) {
            promotionElement.addEventListener('click', copyWechatName);
        }

        console.log('🚀 Augment Code 社区版切换工具已加载');
        console.log('🚀 关注公众号：@煎饼果子卷AI');
    }

    // 启动脚本
    init();

})();